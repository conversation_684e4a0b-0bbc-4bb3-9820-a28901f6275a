---
type: "always_apply"
---

# Scratchpad

## 当前任务：彻底根除挖矿程序（进程ID 3826769）
**任务描述：** 彻底清除具有自动重启机制的挖矿程序

**执行计划：**
[ ] 第一阶段：进程分析
  [ ] 1.1 确认目标进程状态
  [ ] 1.2 重新定位当前挖矿进程
  [ ] 1.3 分析完整进程树
  [ ] 1.4 识别所有相关进程ID
[ ] 第二阶段：自动重启机制调查
  [ ] 2.1 检查crontab任务
  [ ] 2.2 检查systemd服务
  [ ] 2.3 检查启动脚本
  [ ] 2.4 检查用户启动文件
  [ ] 2.5 查找守护进程
[ ] 第三阶段：彻底清除
  [ ] 3.1 备份分析信息
  [ ] 3.2 禁用自动启动机制
  [ ] 3.3 终止所有挖矿进程
  [ ] 3.4 删除程序文件
  [ ] 3.5 验证GPU资源释放

**安全要求：**
- 操作前备份信息
- 不影响合法程序
- 记录恶意文件路径

**安全要求：**
- 严禁使用kill命令
- 只进行只读操作
- 不影响其他程序运行

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- After writing the test code and ensuring all functions are correct, you should delete the test code files to avoid cluttering the project. Meanwhile, all test files should start with "Test_".
- No matter whether the user inputs in Chinese or English, you should default to replying in Chinese, unless the user requests you to reply in English.
- Please note that it is currently July 2025.
- Be sure to use an empty card for inference; before running memory-intensive programs, check the memory usage with nvidia-smi to avoid conflicts with other programs.

## augment learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
