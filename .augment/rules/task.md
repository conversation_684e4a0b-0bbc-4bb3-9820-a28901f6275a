---
type: "always_apply"
---

# Scratchpad

## 当前任务：删除zwc用户账户（保留数据目录）
**任务描述：** 安全删除zwc用户账户，保留/data1/zwc/目录及其内容

**执行计划：**
[x] 第一阶段：安全检查
  [x] 1.1 检查zwc用户的活动进程
  [x] 1.2 终止所有zwc用户进程
  [x] 1.3 验证数据目录状态
  [x] 1.4 备份重要信息
[x] 第二阶段：用户账户删除
  [x] 2.1 使用userdel删除用户
  [x] 2.2 验证用户删除成功
  [x] 2.3 检查系统用户列表
[x] 第三阶段：数据目录验证
  [x] 3.1 确认/data1/zwc/目录完整性
  [x] 3.2 检查目录权限
  [x] 3.3 调整权限便于管理员访问
[x] 第四阶段：最终验证
  [x] 4.1 确认zwc用户无法登录
  [x] 4.2 验证数据目录可访问性
  [x] 4.3 检查系统服务正常运行

**操作结果：**
- zwc用户账户已成功删除
- /data1/zwc/目录完整保留（1.5TB数据）
- 所有挖矿进程已终止
- GPU资源完全释放
- 目录权限已调整为root:root

**安全要求：**
- 操作前备份信息
- 不影响合法程序
- 记录恶意文件路径

**安全要求：**
- 严禁使用kill命令
- 只进行只读操作
- 不影响其他程序运行

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- After writing the test code and ensuring all functions are correct, you should delete the test code files to avoid cluttering the project. Meanwhile, all test files should start with "Test_".
- No matter whether the user inputs in Chinese or English, you should default to replying in Chinese, unless the user requests you to reply in English.
- Please note that it is currently July 2025.
- Be sure to use an empty card for inference; before running memory-intensive programs, check the memory usage with nvidia-smi to avoid conflicts with other programs.

## augment learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
