---
type: "always_apply"
---

# Scratchpad

## 当前任务：调查进程ID 3109463
**任务描述：** 详细调查可疑进程，判断是否为挖矿程序或恶意软件

**调查计划：**
[x] 1. 基础进程信息收集（ps, top等）
[x] 2. 进程详细信息分析（执行路径、启动命令）
[x] 3. 网络连接状态检查
[x] 4. 文件和目录访问分析
[x] 5. 资源使用情况分析（CPU、内存、GPU）
[x] 6. 进程关系分析（父子进程）
[x] 7. 安全性评估和报告生成

**调查结果摘要：**
- 确认为挖矿程序（T-Rex miner）
- 挖矿算法：kawpow（Ravencoin）
- 占用8张A800 GPU，每张约5.6GB显存
- 连接本地矿池代理（localhost:9200）
- 已运行超过1天

**安全要求：**
- 严禁使用kill命令
- 只进行只读操作
- 不影响其他程序运行

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- After writing the test code and ensuring all functions are correct, you should delete the test code files to avoid cluttering the project. Meanwhile, all test files should start with "Test_".
- No matter whether the user inputs in Chinese or English, you should default to replying in Chinese, unless the user requests you to reply in English.
- Please note that it is currently July 2025.
- Be sure to use an empty card for inference; before running memory-intensive programs, check the memory usage with nvidia-smi to avoid conflicts with other programs.

## augment learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
